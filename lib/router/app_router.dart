import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/constant/enum_constant.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/model/chain/token_item_model.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/screen/ai/ai_screen.dart';
import 'package:toii_social/screen/chat/chat_screen.dart';
import 'package:toii_social/screen/home/<USER>/create_post_screen.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/login/login_screen.dart';
import 'package:toii_social/screen/main_tabbar/main_tabbar_screen.dart';
import 'package:toii_social/screen/onboarding/onboarding_screen.dart';
import 'package:toii_social/screen/register/register/register_screen.dart';
import 'package:toii_social/screen/register/verify/create_name/register_create_name_screen.dart';
import 'package:toii_social/screen/register/verify/create_password/register_create_password_screen.dart';
import 'package:toii_social/screen/register/verify/create_pin/register_create_pin_screen.dart';
import 'package:toii_social/screen/register/verify/create_wallet/register_create_wallet_screen.dart';
import 'package:toii_social/screen/register/verify/register_verify_phone_screen.dart';
import 'package:toii_social/screen/register/verify/veriry/register_verify_screen.dart';
import 'package:toii_social/screen/splash/splash_screen.dart';
import 'package:toii_social/screen/start/start_screen.dart';
import 'package:toii_social/screen/user_profile/follower_following/follower_following_screen.dart';
import 'package:toii_social/screen/user_profile/setting_profile/setting_profile_screen.dart';
import 'package:toii_social/screen/user_profile/user_profile_screen.dart';
import 'package:toii_social/screen/wallet/confirm_send/confirm_send_screen.dart';
import 'package:toii_social/screen/wallet/enter_amount/enter_amount_screen.dart';
import 'package:toii_social/screen/wallet/import_token/import_token_screen.dart';
import 'package:toii_social/screen/wallet/receive/receive_token_screen.dart';
import 'package:toii_social/screen/wallet/scanqr/scan_qr_screen.dart';
import 'package:toii_social/screen/wallet/select_token/select_token_screen.dart';
import 'package:toii_social/screen/wallet/send_token/send_token_screen.dart';
import 'package:toii_social/screen/wallet/token_details/token_details_screen.dart';
import 'package:toii_social/utils/navigation_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

enum RouterEnums {
  splash('/splash'),
  onboarding('/onboarding'),
  inital('/'),
  start('/start'),
  register('/register'),
  registerVerify('/register-verify'),

  registerVerifyPhone('/register-verify-phone'),
  registerCreateName('/register-create-name'),
  registerCreatePassword('/register-create-password'),
  registerCreatePin('/register-create-pin'),
  registerCreateWallet('/register-create-wallet'),
  login('/login'),
  homeMain('/home-main'),
  mainTabbar('/main-tabbar'),
  createPost('/create-post'),
  chatScreen('/chat'),
  devicesList('/devices-list'),
  ai('/ai'),
  homeDetails('/home-details'),
  detailPreviewImg('/detail-preview-img'),
  userProfile('/user-profile'),
  settingProfile('/setting-profile'),
  followerFollowing('/follower-following'),
  selectedToken('/selected-token'),
  sendToken('/sendToken'),
  enterAmount('/enter-amount'),
  confirmSendToken('/confirm-send-token'),
  scanQr('/scan-qr'),
  importToken('/importToken'),
  tokenDetails('/token-details'),
  receiveToken('/receiveToken');

  final String routeName;

  const RouterEnums(this.routeName);
}

final registerNavigatorKey = GlobalKey<NavigatorState>();
final GoRouter router = GoRouter(
  initialLocation: RouterEnums.inital.routeName,
  navigatorKey: GetIt.instance<NavigationService>().navigatorKey,
  debugLogDiagnostics: true,
  routes: [
    GoRoute(
      path: RouterEnums.chatScreen.routeName,
      builder: (context, state) => const ChatScreen(),
    ),
    GoRoute(
      path: RouterEnums.start.routeName,
      builder: (context, state) => const StartScreen(),
    ),
    GoRoute(
      path: RouterEnums.splash.routeName,
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: RouterEnums.onboarding.routeName,
      builder: (context, state) => const OnboardingScreen(),
    ),
    GoRoute(
      path: RouterEnums.login.routeName,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: RouterEnums.ai.routeName,
      builder: (context, state) => const AiScreen(),
    ),
    ShellRoute(
      navigatorKey: registerNavigatorKey,
      builder: (context, state, child) => RegisterVerifyScreen(child: child),
      routes: [
        GoRoute(
          path: RouterEnums.registerVerifyPhone.routeName,
          name: RegisterVerifyType.verifyOTP.name,
          builder:
              (context, state) => RegisterVerifyPhoneScreen(
                arg: state.extra as RegisterVerifyPhoneArguments,
              ),
        ),

        GoRoute(
          path: RouterEnums.registerCreateName.routeName,
          name: RegisterVerifyType.createName.name,
          builder:
              (context, state) => RegisterCreateNameScreen(
                registerType: state.extra as RegisterType,
              ),
        ),

        GoRoute(
          path: RouterEnums.registerCreatePassword.routeName,
          name: RegisterVerifyType.createPassword.name,
          builder: (context, state) => const RegisterCreatePasswordScreen(),
        ),
        GoRoute(
          path: RouterEnums.registerCreatePin.routeName,
          name: RegisterVerifyType.createPin.name,
          builder: (context, state) => const RegisterCreatePinScreen(),
        ),
        GoRoute(
          path: RouterEnums.registerCreateWallet.routeName,
          name: RegisterVerifyType.createWallet.name,
          builder: (context, state) => const RegisterCreateWalletScreen(),
        ),
      ],
    ),
    GoRoute(
      path: RouterEnums.register.routeName,
      builder: (context, state) => const RegisterScreen(),
    ),
    GoRoute(
      path: RouterEnums.detailPreviewImg.routeName,
      builder:
          (context, state) =>
              DetailPreviewImg(arg: state.extra as DetailPreviewImgArg),
    ),

    // GoRoute(
    //   path: RouterEnums.registerVerifyPhone.routeName,
    //   builder:
    //       (context, state) => RegisterVerifyPhoneScreen(
    //         arg: state.extra as RegisterVerifyPhoneArguments,
    //       ),
    // ),
    // GoRoute(
    //   path: RouterEnums.registerVerify.routeName,
    //   builder: (context, state) => const RegisterVerifyScreen(),
    // ),
    // GoRoute(
    //   path: RouterEnums.homeMain.routeName,
    //   builder: (context, state) => const HomeMainPage(),
    // ),
    GoRoute(
      path: RouterEnums.mainTabbar.routeName,
      builder: (context, state) => const MainTabbarScreen(),
    ),

    GoRoute(
      path: RouterEnums.createPost.routeName,
      builder:
          (context, state) => CreatePostScreen(post: state.extra as PostModel?),
    ),

    GoRoute(
      path: RouterEnums.homeDetails.routeName,
      builder:
          (context, state) =>
              HomeDetailsScreen(arg: state.extra as HomeDetailsArguments),
    ),

    GoRoute(
      path: RouterEnums.userProfile.routeName,
      builder:
          (context, state) =>
              UserProfileScreen(user: state.extra as UserModel?),
    ),
    GoRoute(
      path: RouterEnums.settingProfile.routeName,
      builder: (context, state) => const SettingProfileScreen(),
    ),
    GoRoute(
      path: RouterEnums.followerFollowing.routeName,
      builder:
          (context, state) =>
              FollowerFollowingScreen(user: state.extra as UserModel?),
    ),
    GoRoute(
      path: RouterEnums.receiveToken.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          fullscreenDialog: true,
          key: state.pageKey,
          child: ReceiveTokenScreen(token: state.extra as TokenItemModel?),
        );
      },
    ),

    GoRoute(
      path: RouterEnums.selectedToken.routeName,
      builder: (context, state) => const SelectTokenScreen(),
    ),
    GoRoute(
      path: RouterEnums.sendToken.routeName,
      builder:
          (context, state) =>
              SendTokenScreen(token: state.extra as TokenItemModel),
    ),
    GoRoute(
      path: RouterEnums.enterAmount.routeName,
      builder:
          (context, state) =>
              EnterAmountScreen(arg: state.extra as EnterAmountArgs),
    ),
    GoRoute(
      path: RouterEnums.confirmSendToken.routeName,
      builder:
          (context, state) =>
              ConfirmSendScreen(arg: state.extra as ConfirmSendScreenArgs),
    ),
    GoRoute(
      path: RouterEnums.scanQr.routeName,
      builder: (context, state) => const ScanQrScreen(),
    ),
    GoRoute(
      path: RouterEnums.importToken.routeName,
      builder: (context, state) => const ImportTokenScreen(),
    ),
    GoRoute(
      path: RouterEnums.tokenDetails.routeName,
      builder: (context, state) {
        final token = state.extra as TokenItemModel;
        return TokenDetailsScreen(token: token);
      },
    ),
  ],
  redirect: (context, state) async {
    final path = state.uri.path;
    if (path == RouterEnums.inital.routeName) {
      final accessToken = SharedPref.getString(KeyShared.tokenKey);
      final isLogged = SharedPref.getBool(KeyShared.isLogin) ?? false;

      if (accessToken.isNotEmpty && isLogged == true) {
        return RouterEnums.mainTabbar.routeName;
      } else {
        return RouterEnums.start.routeName;
      }
    } else {
      return state.uri.path;
    }
  },
);
