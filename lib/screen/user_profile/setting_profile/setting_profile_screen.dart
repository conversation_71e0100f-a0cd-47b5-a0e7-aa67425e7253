import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/user_profile/setting_profile/widget/setting_list_item.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class SettingProfileScreen extends StatelessWidget {
  const SettingProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(title: 'Setting', centerTitle: false),
      body: Column(
        children: [
          // User Profile Section
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: themeData.neutral100,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(shape: BoxShape.circle),
                  child: ClipOval(
                    child: Assets.images.avatarSample.image(fit: BoxFit.cover),
                  ),
                ),
                const SizedBox(width: 12),
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Alice',
                        style: titleMedium.copyWith(
                          color: themeData.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Alice2432',
                        style: labelMedium.copyWith(
                          color: themeData.textSecondary,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
                // Arrow
                SizedBox(
                  width: 24,
                  height: 24,
                  child: Assets.icons.icArrowRight.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.textSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Settings List
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: themeData.neutral50,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  SettingListItem(
                    icon: Assets.icons.icNotification.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Notifications',
                    onTap: () {
                      // TODO: Navigate to notifications settings
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Icon(
                      Icons.block,
                      color: themeData.textSecondary,
                      size: 24,
                    ),
                    title: 'Blocked',
                    onTap: () {
                      // TODO: Navigate to blocked users
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Icon(
                      Icons.security,
                      color: themeData.textSecondary,
                      size: 24,
                    ),
                    title: 'Account privacy',
                    onTap: () {
                      // TODO: Navigate to account privacy
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Icon(
                      Icons.language,
                      color: themeData.textSecondary,
                      size: 24,
                    ),
                    title: 'Language',
                    onTap: () {
                      // TODO: Navigate to language settings
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Icon(
                      Icons.palette,
                      color: themeData.textSecondary,
                      size: 24,
                    ),
                    title: 'Theme',
                    onTap: () {
                      // TODO: Navigate to theme settings
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Assets.icons.icLock.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Privacy Policy',
                    onTap: () {
                      // TODO: Navigate to privacy policy
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Icon(
                      Icons.description,
                      color: themeData.textSecondary,
                      size: 24,
                    ),
                    title: 'Terms of Service',
                    onTap: () {
                      // TODO: Navigate to terms of service
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Icon(
                      Icons.logout,
                      color: themeData.textSecondary,
                      size: 24,
                    ),
                    title: 'Log out',
                    onTap: () {
                      // TODO: Handle logout
                    },
                    showArrow: false,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDivider(ThemeData themeData) {
    return Container(
      height: 1,
      margin: const EdgeInsets.only(left: 56),
      color: themeData.neutral200,
    );
  }
}
