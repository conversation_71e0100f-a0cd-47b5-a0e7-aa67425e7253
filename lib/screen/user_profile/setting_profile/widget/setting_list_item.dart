import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class SettingListItem extends StatelessWidget {
  final Widget icon;
  final String title;
  final VoidCallback? onTap;
  final bool showArrow;

  const SettingListItem({
    super.key,
    required this.icon,
    required this.title,
    this.onTap,
    this.showArrow = true,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            // Icon
            SizedBox(width: 24, height: 24, child: icon),
            const SizedBox(width: 16),
            // Title
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: themeData.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Arrow
            if (showArrow)
              SizedBox(
                width: 24,
                height: 24,
                child: SvgPicture.asset(
                  'icon/arrow_right.svg',
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(
                    themeData.textSecondary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
